<template>
  <div class="orders-interface">
    <!-- Header row with sorting -->
    <div class="orders-header">
      <div class="header-spacer"></div>
      <div class="header-size" @click="onClickSort('size')" :class="getSortClass('size')">
        SIZE
        <q-icon :name="getSortIcon('size')" size="xs" />
      </div>
      <div class="header-price" @click="onClickSort('price')" :class="getSortClass('price')">
        <span class="price-symbol">¢</span>
        <q-icon :name="getSortIcon('price')" size="xs" />
      </div>
      <div class="header-action"></div>
    </div>

    <!-- Loading state -->
    <div v-if="isLoading" class="loading-container">
      <q-spinner size="md" />
    </div>

    <!-- Order groups -->
    <div v-else class="orders-grid">
      <div
        v-for="group in sortedOrderGroups"
        :key="group.eventId"
        class="order-group-subgrid"
      >
        <!-- Event icon with rowspan all -->
        <div class="event-icon-cell">
          <img :src="group.eventIcon || '/img/default_event.webp'" :alt="group.eventTitle" />
        </div>

        <!-- Event title with colspan all -->
        <div class="event-title-cell">
          {{ group.eventTitle }}
        </div>

        <!-- Market groups and orders -->
        <template v-for="market in group.markets" :key="`${group.eventId}-${market.marketTitle}`">
          <!-- Market title (only if not blank) with colspan all -->
          <template v-if="market.marketTitle && market.marketTitle !== 'Unknown Market'">
            <div class="empty-cell"></div>
            <div class="market-title-cell">{{ market.marketTitle }}</div>
          </template>

          <!-- Order rows for this market -->
          <template v-for="order in market.orders" :key="order.id">
            <div class="empty-cell"></div>
            <div class="order-side-cell">
              <span :class="getSideClass(order)">{{ order.side }}</span>
              <span :class="getOutcomeClass(order)">{{ order.outcome }}</span>
            </div>
            <div class="order-filled-cell">{{ formatDecimal(Number(order.size_matched), 0) }}</div>
            <div class="order-separator-cell">/</div>
            <div class="order-amount-cell">{{ formatDecimal(Number(order.original_size), 0) }}</div>
            <div class="order-at-cell">@</div>
            <div class="order-price-cell">{{ formatCents(Number(order.price), 1) }}</div>
            <div class="order-action-cell">
              <q-btn size="xs" flat round class="text-negative" title="Cancel order"
                @click="onClickCancelOrder(order)"
              >
                <template v-slot:default>
                  <q-icon name="close" class="cancel-icon" />
                </template>
              </q-btn>
            </div>
          </template>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { PolyClobOpenOrder, PolyGammaMarket } from "@shared/api-dataclasses-shared";
import { formatCents, formatDecimal } from "src/utils";
import { useApi } from "src/api";
import { useOrderStore } from "src/stores/order-store";
import { useNotifier } from "src/notifier";

interface OrderWithMarketInfo extends PolyClobOpenOrder {
  marketTitle?: string;
  eventTitle?: string;
  eventIcon?: string;
  eventId?: string;
  outcomeAName: string;
}

interface MarketGroup {
  marketTitle: string;
  orders: OrderWithMarketInfo[];
}

interface OrderGroup {
  eventId: string;
  eventTitle: string;
  eventIcon?: string;
  markets: MarketGroup[];
  totalSize: number;
  highestPrice: number;
}

const props = defineProps<{
  userProxyWallet: string;
}>();

const api = useApi();
const orderStore = useOrderStore();
const notifier = useNotifier();

const isLoading = ref(false);
const orders = ref<OrderWithMarketInfo[]>([]);
const sortBy = ref("size");
const sortDesc = ref(true);

const sortedOrderGroups = computed(() => {
  if (!orders.value.length) return [];

  //Group orders by event
  const groupsMap = new Map<string, OrderGroup>();

  for (const order of orders.value) {
    const eventId = order.eventId || 'unknown';

    if (!groupsMap.has(eventId)) {
      groupsMap.set(eventId, {
        eventId,
        eventTitle: order.eventTitle || 'Unknown Event',
        eventIcon: order.eventIcon,
        markets: [],
        totalSize: 0,
        highestPrice: 0
      });
    }

    const group = groupsMap.get(eventId)!;
    group.totalSize += Number(order.original_size) || 0;
    group.highestPrice = Math.max(group.highestPrice, Number(order.price) || 0);

    //Find or create market group within this event
    const marketTitle = order.marketTitle || 'Unknown Market';
    let marketGroup = group.markets.find(m => m.marketTitle === marketTitle);

    if (!marketGroup) {
      marketGroup = {
        marketTitle,
        orders: []
      };
      group.markets.push(marketGroup);
    }

    marketGroup.orders.push(order);
  }

  //Convert to array and sort
  const groups = Array.from(groupsMap.values());

  return groups.sort((a, b) => {
    let aVal: number, bVal: number;

    if (sortBy.value === "size") {
      aVal = a.totalSize;
      bVal = b.totalSize;
    }
    else { //price
      aVal = a.highestPrice;
      bVal = b.highestPrice;
    }

    if (aVal < bVal) return sortDesc.value ? 1 : -1;
    if (aVal > bVal) return sortDesc.value ? -1 : 1;
    return 0;
  });
});

async function loadOrders() {
  if (!props.userProxyWallet) return;

  isLoading.value = true;

  try {
    //Get open orders
    const openOrders = await api.getOrders();

    if (!openOrders.length) {
      orders.value = [];
      return;
    }

    //Get unique condition IDs for market lookup
    const conditionIds = [...new Set(openOrders.map(order => order.market))];

    //Get market data for all condition IDs
    const markets = await api.getMarkets(undefined, conditionIds);

    //Create lookup map for markets by condition ID
    const marketLookup = new Map<string, PolyGammaMarket>();
    for (const market of markets) {
      marketLookup.set(market.conditionId, market);
    }

    //Enhance orders with market information
    const enhancedOrders: OrderWithMarketInfo[] = openOrders.map(order => {
      const market = marketLookup.get(order.market);

      if (!market) {
        console.log("UserOrderInterface: failed to load market data for order", order);
        throw new Error("CLIENT: UserOrderInterface failed to load market data for order");
      }

      //Handle case where market might not have events array or it might be empty
      const event = market.events && market.events.length > 0 ? market.events[0] : null;
      if (!event) {
        console.log("UserOrderInterface: failed to load event data for order", order, market);
        throw new Error("CLIENT: UserOrderInterface failed to load event data for order");
      }

      return {
        ...order,
        marketTitle: market.groupItemTitle,
        eventTitle: event.title || market.question || 'Unknown Event',
        eventIcon: event.icon || market.icon,
        eventId: event.id || market.id || 'unknown',
        outcomeAName: (market.outcomes as string).match(/"(.*?)",/)![1]
      };
    });

    orders.value = enhancedOrders;
  }
  finally {
    isLoading.value = false;
  }
}

function onClickSort(column: string) {
  if (sortBy.value === column) {
    sortDesc.value = !sortDesc.value;
  }
  else {
    sortBy.value = column;
    sortDesc.value = true; //Default to descending
  }
}

function getSortClass(column: string) {
  return {
    "sort-active": sortBy.value === column,
    "cursor-pointer": true
  };
}

function getSortIcon(column: string) {
  if (sortBy.value !== column) return "unfold_more";
  return sortDesc.value ? "keyboard_arrow_down" : "keyboard_arrow_up";
}

function getSideClass(order: OrderWithMarketInfo) {
  if (order.side === "BUY") {
    return ["bg-yes", "text-bold", "text-black"];
  }
  else if (order.side === "SELL") {
    return ["bg-no", "text-bold", "text-black"];
  }
}

function getOutcomeClass(order: OrderWithMarketInfo) {
  if (order.outcome === order.outcomeAName) {
    return ["text-yes"];
  }
  else if (order.outcome !== order.outcomeAName) {
    return ["text-no"];
  }
}



async function onClickCancelOrder(order: OrderWithMarketInfo) {
  try {
    const res = await orderStore.cancelOrder([order.id]);

    if (res.canceled.length) {
      notifier.success(`${order.side} Order @ ${formatCents(Number(order.price))} CANCELED`);
      //Reload orders to reflect the cancellation
      await loadOrders();
    }
    if (Object.keys(res.not_canceled).length) {
      const notCanceled = Object.values(res.not_canceled);
      notifier.error(`Cancel failed: ${notCanceled.join(', ')}`);
    }
  }
  catch (error) {
    console.error('Error canceling order:', error);
  }
}

//Watch for userProxyWallet changes to reload orders
watch(() => props.userProxyWallet, () => {
  loadOrders();
}, { immediate: true });



onMounted(() => {
  loadOrders();
});
</script>

<style scoped lang="scss">
.orders-interface {
  display: flex;
  flex-direction: column;
  width: 100%;
  font-family: 'OpenSauceSans';
}

.orders-header {
  display: grid;
  grid-template-columns: 60px max-content max-content max-content max-content max-content max-content 1fr;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e0e0e0;
  font-weight: 600;
  font-size: 12px;
  color: #666;
}

.header-spacer {
  grid-column: 1;
}

.header-size {
  grid-column: 2 / 6;
  display: flex;
  align-items: center;
  gap: 4px;
}

.header-price {
  grid-column: 7;
  display: flex;
  align-items: center;
  gap: 4px;
}

.price-symbol {
  font-size: 14px;
  font-weight: 700;
}

.header-action {
  grid-column: 8;
}

.sort-active {
  color: #1976d2;
}

.loading-container {
  display: flex;
  justify-content: center;
  padding: 40px;
}

.orders-grid {
  display: grid;
  grid-template-columns: 60px max-content max-content max-content max-content max-content max-content 1fr;
  font-size: 12px;
}

.order-group-subgrid {
  display: grid;
  grid-template-columns: subgrid;
  grid-column: 1 / -1;
  border-bottom: 1px solid #f0f0f0;
  padding: 6px 0;
}

.event-icon-cell {
  grid-row: 1 / -1;
  grid-column: 1;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 4px;

  img {
    width: 42px;
    height: 42px;
    border-radius: 4px;
    object-fit: cover;
  }
}

.event-title-cell {
  grid-column: 2 / -1;
  grid-row: 1;
  font-weight: 700;
  font-size: 14px;
  color: #333;
  padding: 2px 0;
  height: fit-content;
}

.market-title-cell {
  grid-column: 2 / -1;
  font-size: 1px;
  color: #888;
  font-weight: 500;
  padding: 2px 0;
}

.empty-cell {
  grid-column: 1;
}

.order-side-cell {
  grid-column: 2;
  font-weight: 600;
  border-radius: 4px;
  padding: 2px 6px;
  display: flex;
  gap: 4px;
  align-items: center;
  background-color: $OrderBackground;
  color: $OrderPrimary;
  margin-bottom: 2px;
}

.order-filled-cell {
  grid-column: 3;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: $OrderBackground;
  color: $OrderPrimary;
  margin-bottom: 2px;
}

.order-separator-cell {
  grid-column: 4;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: $OrderBackground;
  color: $OrderPrimary;
  margin-bottom: 2px;
}

.order-amount-cell {
  grid-column: 5;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: $OrderBackground;
  color: $OrderPrimary;
  margin-bottom: 2px;
}

.order-at-cell {
  grid-column: 6;
  text-align: center;
  padding-left: 4px;
  padding-right: 2px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: $OrderBackground;
  color: $OrderPrimary;
  margin-bottom: 2px;
}

.order-price-cell {
  grid-column: 7;
  text-align: right;
  font-weight: 600;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: $OrderBackground;
  color: $OrderPrimary;
  margin-bottom: 2px;
}

.order-action-cell {
  grid-column: 8;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: $OrderBackground;
  margin-bottom: 2px;
}

.cancel-icon {
  margin-top: 4px;
}
</style>
